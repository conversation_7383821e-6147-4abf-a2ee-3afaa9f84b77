'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { 
  testChatbotHealth, 
  testChatbotAuth, 
  testCreateConversation,
  runChatbotDiagnostics,
  getChatbotApiConfig,
  clearChatbotData,
  ChatbotDebugResult 
} from '../../utils/debug-chatbot';
import { CheckCircle, XCircle, AlertCircle, Loader2, RefreshCw } from 'lucide-react';

export default function DebugChatbotPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [assessmentId, setAssessmentId] = useState('result-001');
  const [apiConfig, setApiConfig] = useState(getChatbotApiConfig());

  const runTest = async (testName: string, testFunction: () => Promise<any>) => {
    setIsLoading(true);
    try {
      const result = await testFunction();
      setResults(prev => ({ ...prev, [testName]: result }));
    } catch (error) {
      setResults(prev => ({ 
        ...prev, 
        [testName]: { 
          success: false, 
          message: error.message,
          timestamp: new Date().toISOString()
        } 
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const runAllTests = async () => {
    setIsLoading(true);
    setResults({});
    
    try {
      const diagnostics = await runChatbotDiagnostics(assessmentId);
      setResults({
        comprehensive: {
          success: diagnostics.overall,
          message: diagnostics.overall ? 'All tests passed' : 'Some tests failed',
          details: diagnostics.results,
          timestamp: new Date().toISOString()
        },
        ...diagnostics.results
      });
    } catch (error) {
      setResults({
        comprehensive: {
          success: false,
          message: 'Diagnostics failed',
          details: { error: error.message },
          timestamp: new Date().toISOString()
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearData = () => {
    const result = clearChatbotData();
    setResults(prev => ({
      ...prev,
      clearData: {
        success: true,
        message: `Cleared ${result.clearedKeys.length} conversations`,
        details: result,
        timestamp: new Date().toISOString()
      }
    }));
  };

  const refreshConfig = () => {
    setApiConfig(getChatbotApiConfig());
  };

  const ResultCard = ({ title, result }: { title: string; result: ChatbotDebugResult }) => (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          {result.success ? (
            <CheckCircle className="w-4 h-4 text-green-600" />
          ) : (
            <XCircle className="w-4 h-4 text-red-600" />
          )}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className={`text-sm mb-2 ${result.success ? 'text-green-700' : 'text-red-700'}`}>
          {result.message}
        </p>
        {result.details && (
          <details className="text-xs text-gray-600">
            <summary className="cursor-pointer mb-2">Details</summary>
            <pre className="bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(result.details, null, 2)}
            </pre>
          </details>
        )}
        <p className="text-xs text-gray-500 mt-2">
          {new Date(result.timestamp).toLocaleString()}
        </p>
      </CardContent>
    </Card>
  );

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">🧪 Chatbot API Debug Tool</h1>
        <p className="text-gray-600">
          Test and debug the chatbot API integration
        </p>
      </div>

      {/* API Configuration */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5" />
            API Configuration
            <Button variant="outline" size="sm" onClick={refreshConfig}>
              <RefreshCw className="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Base URL:</strong> {apiConfig.baseUrl}
            </div>
            <div>
              <strong>Timeout:</strong> {apiConfig.timeout}ms
            </div>
            <div>
              <strong>Token:</strong> {apiConfig.token ? '✅ Present' : '❌ Missing'}
            </div>
            <div>
              <strong>Token Length:</strong> {apiConfig.token?.length || 0}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Test Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Assessment ID for testing:
              </label>
              <Input
                value={assessmentId}
                onChange={(e) => setAssessmentId(e.target.value)}
                placeholder="Enter assessment ID"
              />
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Button 
                onClick={runAllTests}
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isLoading ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : null}
                Run All Tests
              </Button>
              
              <Button 
                variant="outline"
                onClick={() => runTest('health', testChatbotHealth)}
                disabled={isLoading}
              >
                Test Health
              </Button>
              
              <Button 
                variant="outline"
                onClick={() => runTest('auth', testChatbotAuth)}
                disabled={isLoading}
              >
                Test Auth
              </Button>
              
              <Button 
                variant="outline"
                onClick={() => runTest('conversation', () => testCreateConversation(assessmentId))}
                disabled={isLoading}
              >
                Test Conversation
              </Button>
              
              <Button 
                variant="outline"
                onClick={handleClearData}
                className="text-red-600 hover:text-red-700"
              >
                Clear Data
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {results && (
        <div>
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          
          {results.comprehensive && (
            <ResultCard title="Comprehensive Test" result={results.comprehensive} />
          )}
          
          {results.health && (
            <ResultCard title="Health Check" result={results.health} />
          )}
          
          {results.auth && (
            <ResultCard title="Authentication" result={results.auth} />
          )}
          
          {results.conversation && (
            <ResultCard title="Conversation Creation" result={results.conversation} />
          )}
          
          {results.clearData && (
            <ResultCard title="Clear Data" result={results.clearData} />
          )}
        </div>
      )}

      {/* Instructions */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Instructions</CardTitle>
        </CardHeader>
        <CardContent className="text-sm text-gray-600">
          <ol className="list-decimal list-inside space-y-2">
            <li>Make sure you're logged in and have a valid authentication token</li>
            <li>Run "Test Health" to check if the chatbot API is accessible</li>
            <li>Run "Test Auth" to verify your authentication is working</li>
            <li>Run "Test Conversation" to test creating a new conversation</li>
            <li>Use "Run All Tests" for a comprehensive check</li>
            <li>Check the browser console for detailed logs</li>
          </ol>
        </CardContent>
      </Card>
    </div>
  );
}
